/*
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-08-20 14:37:25
 * @LastEditTime: 2025-08-20 14:55:02
 * @FilePath: /逆向百例/小红书/main.go
 */
package main

import (
	"fmt"
	"io"
	"log"
	"net/http"
	"strings"
)

func main() {
	client := &http.Client{}
	var data = strings.NewReader(`{"source_note_id":"687f62550000000017033e10","image_formats":["jpg","webp","avif"],"extra":{"need_body_topic":"1"},"xsec_source":"pc_feed","xsec_token":"ABYhe9A9e91kZ4kdVfKRMmkX2uGxQKqolbXkwLqGE5_m0="}`)
	req, err := http.NewRequest("POST", "https://edith.xiaohongshu.com/api/sns/web/v1/feed", data)
	if err != nil {
		log.Fatal(err)
	}
	req.Header.Set("accept", "application/json, text/plain, */*")
	req.Header.Set("accept-language", "zh-CN,zh;q=0.9")
	req.Header.Set("cache-control", "no-cache")
	req.Header.Set("content-type", "application/json;charset=UTF-8")
	req.Header.Set("dnt", "1") 
	req.Header.Set("origin", "https://www.xiaohongshu.com")
	req.Header.Set("pragma", "no-cache")
	req.Header.Set("priority", "u=1, i")
	req.Header.Set("referer", "https://www.xiaohongshu.com/")
	req.Header.Set("sec-ch-ua", `"Google Chrome";v="141", "Not?A_Brand";v="8", "Chromium";v="141"`)
	req.Header.Set("sec-ch-ua-mobile", "?0")
	req.Header.Set("sec-ch-ua-platform", `"Windows"`)
	req.Header.Set("sec-fetch-dest", "empty")
	req.Header.Set("sec-fetch-mode", "cors")
	req.Header.Set("sec-fetch-site", "same-site")
	req.Header.Set("sec-gpc", "1")
	req.Header.Set("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36")
	req.Header.Set("x-b3-traceid", "6fe40d2934f87c74")
	req.Header.Set("x-s", "XYS_2UQhPsHCH0c1PjhMHjIj2erjwjQhyoPTqBPT49pjHjIj2eHjwjQgynEDJ74AHjIj2ePjwjQTJdPIP/ZlgMrU4SmH4bkfwok1+nY1qBz7qbSBy/SOcD8czSQ1zSD7y9TjPSbV+dkbGpD3G9b1LDS8we4dyDkt4FMVzFu9JSY1J78IyL+L4FQrpFRDyn49nDzoJoYPcAQ0zfkVc0Why0bdLbbEy/QCneQB+bYz87Z98FSyzAGUyLWAyAbsaMZ3ynWl4MQIJFT6JDk6+94czeZl8rpV4/q7yLMcnsRc40HlwrS1+pZM4rzzHjIj2ecjwjQ6GfkSG7cjKc==")
	req.Header.Set("x-s-common", "2UQAPsHC+aIjqArjwjHjNsQhPsHCH0rjNsQhPaHCH0c1PjhMHjIj2eHjwjQgynEDJ74AHjIj2ePjwjQhyoPTqBPT49pjHjIj2ecjwjHFN0q9N0ZjNsQh+aHCH0rE+0bfweHFP/qM+B8A+e+jJgZh80q9P7S38o40Jdp749T7PfzVye+f+/ZIPeZU+/LhP0HjNsQh+jHCHjHVHdW7H0ijHjIj2eWjwjQQPAYUaBzdq9k6qB4Q4fpA8b878FSet9RQzLlTcSiM8/+n4MYP8F8LagY/P9Ql4FpUzfpS2BcI8nT1GFbC/L88JdbFyrSiafp/JDMra7pFLDDAa7+8J7QgabmFz7Qjp0mcwp4fanD68p40+fp8qgzELLbILrDA+9p3JpH9LLI3+LSk+d+DJfpSL98lnLYl49IUqgcMc0mrcDShtMmozBD6qM8FyFSh8o+h4g4U+obFyLSi4nbQz/+SPFlnPrDApSzQcA4SPopFJeQmzBMA/o8Szb+NqM+c4ApQzg8Ayp8FaDRl4AYs4g4fLomD8pzBpFRQ2ezLanSM+Skc47Qc4gcMag8VGLlj87PAqgzhagYSqAbn4FYQy7pTanTQ2npx87+8NM4L89L78p+l4BL6ze4AzB+IygmS8Bp8qDzFaLP98Lzn4AQQzLEAL7bFJBEVL7pwyS8Fag868nTl4e+0n04ApfuF8FSbL7SQyrLULnz0pLShJpmO2fM6anS0nBpc4F8Q4fSePDHAqFzC+7+hpdzDagG98nc7+9p8ydL3anDM8/8gq0pwLo4xanYtqA+68gP9zo8SpbmF/f+p+fpr4gqMag88qoiI+npfLo4e/BEwqFzM4MmQP9TIagYTJo+l4o+YLo4Eq7+HGSkm4fLAqsRSzbm72rSe8g+3zemSL9pHyLSk+7+xGfRAP94UzDSk8BL94gqAanSUy7zM4BMF4gzBagYS8pzc4r8QyrkSyp8FJrS389LILoz/t7b7Lokc4MpQ4fY3agY0q0zdarr3aLESypmFyDSiqdzQyBRAydbFLrlILnb7qDTA8B808rSi2juU4g4yqdp7LFSe8o+3Loz/tFMN8/b0cg+k/nMPanSmq7W78g+L4gzEGMm7qLSePBpfpdzpanSw8pzA4pmQcFTSnnuA8p4n4ApQ2rTAPgp74LkspMSQPMQaanSw8nkSP7+raLRAP/mm8p8c49zQc9MSanY0PDkM49kQy9SOagYd8nzAL9EP/nzSpbm74DDApAmYqgq62LGIqM+/P7+/Lo4jGM8FLDSeabYQygbTqop7qgbc47DjNsQhwaHCP/WIP0Zl+AWl+aIj2erIH0iINsQhP/rjwjQ1J7QTGnIjKc==")
	req.Header.Set("x-t", "1755671682700")
	req.Header.Set("x-xray-traceid", "cc6317ef3a571661e9c8a3bf998b65d8")
	req.Header.Set("cookie", "web_session=030037a16bf4ab1ce24606564a204aef071e7c; abRequestId=b6493990-208b-5825-bfb3-49e241c63c39; a1=1961f8241754fs43bmp8f763ykdwcnuwwkw2dlh3f50000255822; webId=da8284cc1d1f79fbfcf5eea5bbf3f467; gid=yjKJ84iyYyE8yjKyiYJ4yT1xW24iM4qD6UYiEqiWKqvTfJ28ESI7Ef888J22YJJ8dDdfjj2K; xsecappid=xhs-pc-web; webBuild=4.76.0; loadts=1755671658156; acw_tc=0a4addf217556716595324556e9574dd5723c42f241df7130af10d4d95bafd; websectiga=7750c37de43b7be9de8ed9ff8ea0e576519e8cd2157322eb972ecb429a7735d4; sec_poison_id=1110bbd6-b74b-40d2-8a92-0157ac184c40; unread={%22ub%22:%226895999a0000000025012e55%22%2C%22ue%22:%22688cb9b000000000230283d7%22%2C%22uc%22:20}")
	resp, err := client.Do(req)
	if err != nil {
		log.Fatal(err)
	}
	defer resp.Body.Close()
	bodyText, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Fatal(err)
	}
	fmt.Printf("%s\n", bodyText)
}
